import os
import sys

# 添加父目录到系统路径，以便能导入mysql转其他库模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入转换函数
from 福建.py.mysql转其他库 import translate_sql

# 测试用户提供的SQL示例
def test_user_example():
    # 原始SQL
    original_sql = """SELECT 'YD01202506171810', '患者过敏记录（PATIENT_ALLERGY_RECORD）中的皮试人员姓名（SKINTST_MEDSTFFNAME）不为空时，只能包含汉字、英文、· ，首位和结尾不能是 · ，纯中文时不可含有空格', COUNT( 1 ) FROM patient_allergy_record A WHERE 1 = 1 AND A.SKINTST_MEDSTFFNAME REGEXP '[^a-zA-Z\\u4e00-\\u9fa5·]' OR A.SKINTST_MEDSTFFNAME LIKE '·%' OR A.SKINTST_MEDSTFFNAME LIKE '%·' OR ( A.SKINTST_MEDSTFFNAME REGEXP '^[\\u4e00-\\u9fa5]+$' AND A.SKINTST_MEDSTFFNAME LIKE '% %' );"""
    
    # 转换为SQL Server
    sqlserver_sql = translate_sql(original_sql, 'sqlserver')
    print("=== 转换为SQL Server的结果 ===")
    print(sqlserver_sql)
    
    # 验证是否包含关键元素
    key_components = [
        "A.SKINTST_MEDSTFFNAME IS NOT NULL",
        "LIKE '%[^a-zA-Z·' + NCHAR(0x4e00) + '-' + NCHAR(0x9fa5) + ']%'",
        "COLLATE Latin1_General_BIN",
        "A.SKINTST_MEDSTFFNAME LIKE '·%'",
        "A.SKINTST_MEDSTFFNAME LIKE '%·'",
        "PATINDEX('%[^' + NCHAR(0x4e00) + '-' + NCHAR(0x9fa5) + ']%'",
        "A.SKINTST_MEDSTFFNAME LIKE '% %'"
    ]
    
    print("\n=== 验证关键元素 ===")
    all_found = True
    for component in key_components:
        if component in sqlserver_sql:
            print(f"✓ 找到: {component}")
        else:
            print(f"✗ 未找到: {component}")
            all_found = False
    
    if all_found:
        print("\n转换成功! 生成的SQL包含所有必要的关键元素!")
    else:
        print("\n转换不完整! 缺少一些关键元素。")

# 测试不同的单独情况
def test_individual_cases():
    print("\n\n=== 测试单独情况 ===")
    
    # 测试情况1：单独测试中文字符校验
    sql1 = """SELECT * FROM patient_allergy_record WHERE SKINTST_MEDSTFFNAME REGEXP '[^a-zA-Z\\u4e00-\\u9fa5·]'"""
    print("\n--- 测试情况1：中文字符校验 ---")
    print("原始SQL: ", sql1)
    sqlserver_sql1 = translate_sql(sql1, 'sqlserver')
    print("转换后: ", sqlserver_sql1)
    
    # 测试情况2：单独测试纯中文验证
    sql2 = """SELECT * FROM patient_allergy_record WHERE SKINTST_MEDSTFFNAME REGEXP '^[\\u4e00-\\u9fa5]+$'"""
    print("\n--- 测试情况2：纯中文验证 ---")
    print("原始SQL: ", sql2)
    sqlserver_sql2 = translate_sql(sql2, 'sqlserver')
    print("转换后: ", sqlserver_sql2)
    
    # 测试情况3：单独测试点号开头检查
    sql3 = """SELECT * FROM patient_allergy_record WHERE SKINTST_MEDSTFFNAME LIKE '·%'"""
    print("\n--- 测试情况3：点号开头检查 ---")
    print("原始SQL: ", sql3)
    sqlserver_sql3 = translate_sql(sql3, 'sqlserver')
    print("转换后: ", sqlserver_sql3)
    
    # 测试情况4：单独测试空格检查
    sql4 = """SELECT * FROM patient_allergy_record WHERE SKINTST_MEDSTFFNAME LIKE '% %'"""
    print("\n--- 测试情况4：空格检查 ---")
    print("原始SQL: ", sql4)
    sqlserver_sql4 = translate_sql(sql4, 'sqlserver')
    print("转换后: ", sqlserver_sql4)

if __name__ == "__main__":
    # 测试主用例
    test_user_example()
    
    # 测试单独情况
    test_individual_cases() 