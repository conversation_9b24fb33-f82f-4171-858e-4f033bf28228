import subprocess
import os

def run_script(script_path):
    """
    调用指定的 Python 脚本。
    """
    try:
        print(f"正在执行脚本: {script_path}")
        result = subprocess.run(['python', script_path], check=True, capture_output=True, text=True, encoding='utf-8', errors='replace')
        print(f"脚本 {script_path} 执行成功 (使用 utf-8 编码)。")
        if result.stdout:
            print("脚本输出:")
            print(result.stdout)
        if result.stderr:
            print("脚本错误:")
            print(result.stderr)
    except subprocess.CalledProcessError as e:
        print(f"脚本 {script_path} 执行失败，错误信息:")
        if e.stderr:
            print(e.stderr.decode('utf-8', errors='replace'))
    except FileNotFoundError:
        print(f"错误: 找不到脚本文件 {script_path}")
    except Exception as e:
        print(f"执行脚本 {script_path} 时发生未知错误: {e}")

if __name__ == "__main__":
    base_path = r"D:\work\demo\福建\py"
    mysql_script = os.path.join(base_path, "生成mysql建表语句.py")
    oracle_script = os.path.join(base_path, "生成oracle建表语句.py")
    sqlserver_script = os.path.join(base_path, "生成sqlserver建表语句.py")
    guass_script = os.path.join(base_path, "生成高斯数据库建表语句-精简版.py")

    run_script(mysql_script)
    run_script(oracle_script)
    run_script(sqlserver_script)
    run_script(guass_script)

    print("所有建表语句生成脚本已执行完毕。")