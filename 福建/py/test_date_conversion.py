from mysql转其他库 import translate_sql, process_single_sql

# 测试用例
test_sql = """SELECT uscid AS uscid,'patient_allergy_record' AS tabname,date_format(rec_time,'%Y%m') AS ny,count(*) AS d_count 
FROM patient_allergy_record 
WHERE rec_time >= '2020-01-01 00:00:00' AND rec_time <= '2025-05-31 23:59:59' 
AND deleted = '0' 
GROUP BY uscid,date_format(rec_time,'%Y%m') 
ORDER BY date_format(rec_time,'%Y%m')"""

# 1. 转换为 Oracle
oracle_sql = translate_sql(test_sql, 'oracle')
print("\n===== Oracle 转换结果 =====")
print(oracle_sql)

# Oracle 预期结果
expected_oracle = """SELECT uscid AS uscid,'patient_allergy_record' AS tabname,TO_CHAR(rec_time, 'YYYYMM') AS ny,count(*) AS d_count 
FROM patient_allergy_record 
WHERE rec_time >= TO_DATE('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') AND rec_time <= TO_DATE('2025-05-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS') 
AND deleted = '0' 
GROUP BY uscid,TO_CHAR(rec_time, 'YYYYMM') 
ORDER BY TO_CHAR(rec_time, 'YYYYMM')"""

print("\n===== Oracle 预期结果 =====")
print(expected_oracle)

# 比较 Oracle 结果
print("\n===== Oracle 结果比较 =====")
if oracle_sql.strip() == expected_oracle.strip():
    print("Oracle 转换成功！结果与预期一致。")
else:
    print("Oracle 转换结果与预期不一致。")
    # 查找差异
    oracle_lines = oracle_sql.strip().split('\n')
    expected_lines = expected_oracle.strip().split('\n')
    
    for i, (actual, expected) in enumerate(zip(oracle_lines, expected_lines)):
        if actual != expected:
            print(f"第 {i+1} 行不匹配:")
            print(f"实际: {actual}")
            print(f"预期: {expected}")

# 2. 转换为 SQL Server
sqlserver_sql = translate_sql(test_sql, 'sqlserver')
print("\n===== SQL Server 转换结果 =====")
print(sqlserver_sql)

# SQL Server 预期结果
expected_sqlserver = """SELECT uscid AS uscid,'patient_allergy_record' AS tabname,FORMAT(rec_time, 'yyyyMM') AS ny,count(*) AS d_count 
FROM patient_allergy_record 
WHERE rec_time >= CONVERT(DATETIME, '2020-01-01 00:00:00', 120) AND rec_time <= CONVERT(DATETIME, '2025-05-31 23:59:59', 120) 
AND deleted = '0' 
GROUP BY uscid,FORMAT(rec_time, 'yyyyMM') 
ORDER BY FORMAT(rec_time, 'yyyyMM')"""

print("\n===== SQL Server 预期结果 =====")
print(expected_sqlserver)

# 比较 SQL Server 结果
print("\n===== SQL Server 结果比较 =====")
if sqlserver_sql.strip() == expected_sqlserver.strip():
    print("SQL Server 转换成功！结果与预期一致。")
else:
    print("SQL Server 转换结果与预期不一致。")
    # 查找差异
    sqlserver_lines = sqlserver_sql.strip().split('\n')
    expected_lines = expected_sqlserver.strip().split('\n')
    
    for i, (actual, expected) in enumerate(zip(sqlserver_lines, expected_lines)):
        if actual != expected:
            print(f"第 {i+1} 行不匹配:")
            print(f"实际: {actual}")
            print(f"预期: {expected}")

# 3. 转换为 GaussDB
gaussdb_sql = translate_sql(test_sql, 'gaussdb')
print("\n===== GaussDB 转换结果 =====")
print(gaussdb_sql)

# GaussDB 预期结果
expected_gaussdb = """SELECT uscid AS uscid,'patient_allergy_record' AS tabname,TO_CHAR(rec_time, 'YYYYMM') AS ny,count(*) AS d_count 
FROM patient_allergy_record 
WHERE rec_time >= TO_TIMESTAMP('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') AND rec_time <= TO_TIMESTAMP('2025-05-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS') 
AND deleted = '0' 
GROUP BY uscid,TO_CHAR(rec_time, 'YYYYMM') 
ORDER BY TO_CHAR(rec_time, 'YYYYMM')"""

print("\n===== GaussDB 预期结果 =====")
print(expected_gaussdb)

# 比较 GaussDB 结果
print("\n===== GaussDB 结果比较 =====")
if gaussdb_sql.strip() == expected_gaussdb.strip():
    print("GaussDB 转换成功！结果与预期一致。")
else:
    print("GaussDB 转换结果与预期不一致。")
    # 查找差异
    gaussdb_lines = gaussdb_sql.strip().split('\n')
    expected_lines = expected_gaussdb.strip().split('\n')
    
    for i, (actual, expected) in enumerate(zip(gaussdb_lines, expected_lines)):
        if actual != expected:
            print(f"第 {i+1} 行不匹配:")
            print(f"实际: {actual}")
            print(f"预期: {expected}") 