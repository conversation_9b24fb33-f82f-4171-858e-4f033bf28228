#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
将质控json数据填充到Excel模板中的多个sheet页中
写一个将txt内容填充到excel的脚本,'D:\work\demo\福建\py\质控问题清单模板json转excel.py'
excel模板路径'D:\work\demo\福建\质控模板\福建-医院质控问题清单模板.xlsx'
txt路径'D:\work\demo\福建\质控json'下所有.txt文件，txt文件的格式是json
excel文件说明：
'医院列表'sheet页不需要操作，取'行政区划'和'医疗机构名称'列，获取医院和行政区划的映射关系，存起来供程序后面调用
其他所有sheet页，分别代表各个行政区划的数据，逐个填充
操作步骤：
只读取txt文件中的rows节点，rows下有多条数据，对应不同行政区划的多个医院数据，但是rows中没有行政区划和医院的关系，所以使用前面存储的对应关系，将数据通过医院名称按照行政区划分组，
先获取key'hosname'（只保留key'hosname'对应的值不为空的数据），对应的value和'医院列表'sheet页的'医疗机构名称'列匹配获取对应的'行政区划','行政区划'的值和sheet页的名称是对应的，将相同行政区划的数据填充到对应的sheet页中。
逐个遍历所有txt文件，文件名截取掉'coreflddq_'前缀后的文本就是表名和excel中的'表名'列匹配，excel的'问题说明'列的值对应的是rows中的key，匹配完表名列和问题说明列后，用key'hosname'的值匹配excel中第一行的表头，获取表头所在的列的列数值，将'问题说明'列的值对应的是rows中的key所对应的值填充到对应的列（这么做的原因是医院名称是两个单元合并的，下面对应质控结果和问题描述两列，我只需要将数据填充到质控结果列）。
总结就是，通过'hosname'的值和医院列表的映射关系来锁定要操作的sheet页，通过表名和问题说明列来锁定要操作的行，通过匹配的'hosname'的值来锁定要填充数据的列。遍历所有txt文件，将对应数据填充到对应的excel中。
将结果输出到'D:\work\demo\福建\质控结果\已填充_福建-医院质控问题清单.xlsx'

将质控json数据填充到Excel模板中的多个sheet页中

读取excel模板'D:\work\demo\福建\质控模板\福建-医院质控问题清单模板.xlsx'
处理'D:\work\demo\福建\质控json'下所有.txt文件（JSON格式）
输出结果到'D:\work\demo\福建\质控结果\已填充_福建-医院质控问题清单.xlsx'

操作流程：
1. 从模板中读取"医院列表"sheet，获取医院与行政区划的映射关系
2. 处理所有txt文件的JSON数据，提取rows节点
3. 根据医院名称和行政区划映射，将数据填充到对应的sheet页中
4. 在sheet页中，根据表名和问题说明定位行，根据医院名称定位列，进行数据填充
"""

# 导入所需库
import os
import json
import pandas as pd
import logging
import datetime
import shutil
from openpyxl import load_workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from copy import copy

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("质控问题清单模板json转excel.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 文件路径配置
TEMPLATE_PATH = r'D:\work\demo\福建\质控模板\福建-医院质控问题清单模板.xlsx'
JSON_DIR = r'D:\work\demo\福建\质控json'
OUTPUT_PATH = r'D:\work\demo\福建\质控结果\已填充_福建-医院质控问题清单.xlsx'

# 如果输出目录不存在，创建它
OUTPUT_DIR = os.path.dirname(OUTPUT_PATH)
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

def get_hospital_area_mapping(template_path):
    """
    从Excel模板中读取"医院列表"sheet，建立医院名称到行政区划的映射
    
    Args:
        template_path (str): Excel模板文件路径
    
    Returns:
        dict: 医院名称到行政区划的映射字典
    """
    try:
        logging.info("开始读取医院列表，建立医院与行政区划的映射关系...")
        
        # 读取医院列表sheet
        hospital_df = pd.read_excel(template_path, sheet_name="医院列表")
        
        # 初始化映射字典
        hospital_to_area = {}
        
        # 遍历行，建立映射关系
        for _, row in hospital_df.iterrows():
            hospital_name = row.get("医疗机构名称")
            area = row.get("行政区划")
            
            # 检查数据有效性
            if pd.notna(hospital_name) and pd.notna(area):
                hospital_to_area[hospital_name] = area
        
        logging.info(f"成功建立医院与行政区划的映射关系，共 {len(hospital_to_area)} 家医院")
        return hospital_to_area
        
    except Exception as e:
        logging.error(f"读取医院列表时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return {}

def extract_table_name(txt_filename):
    """
    从txt文件名中提取表名（去除coreflddq_前缀和.txt后缀）
    
    Args:
        txt_filename (str): txt文件名
    
    Returns:
        str: 提取的表名，如果格式不匹配则返回None
    """
    if txt_filename.startswith("coreflddq_") and txt_filename.endswith(".txt"):
        # 去除前缀"coreflddq_"和后缀".txt"
        return txt_filename[10:-4]
    else:
        logging.warning(f"文件名 {txt_filename} 格式不符合预期，无法提取表名")
        return None

def load_json_data(json_file_path):
    """
    加载JSON文件，并提取rows节点数据，过滤hosname不为空的记录
    
    Args:
        json_file_path (str): JSON文件路径
    
    Returns:
        list: rows节点中hosname不为空的数据列表
        list: JSON文件中的列名列表（用于后续匹配问题说明）
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        # 检查JSON结构是否包含details和rows节点
        if 'details' in json_data and 'rows' in json_data['details']:
            rows = json_data['details']['rows']
            
            # 如果存在columnNames字段，获取列名列表
            column_names = json_data['details'].get('columnNames', [])
            
            # 过滤hosname不为空的记录
            valid_rows = [row for row in rows if row.get('hosname')]
            
            logging.info(f"从 {json_file_path} 加载了 {len(valid_rows)}/{len(rows)} 条有效数据（hosname不为空）")
            return valid_rows, column_names
        else:
            logging.error(f"JSON文件 {json_file_path} 中未找到有效的rows节点")
            return [], []
    except Exception as e:
        logging.error(f"读取JSON文件 {json_file_path} 时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return [], []

def find_row_index_by_table_and_problem(df, table_name, problem_desc):
    """
    在DataFrame中查找匹配表名和问题说明的行索引
    
    Args:
        df (pandas.DataFrame): 工作表数据
        table_name (str): 表名
        problem_desc (str): 问题说明
    
    Returns:
        int: 匹配行的索引，如果未找到则返回None
    """
    try:
        # 检查DataFrame是否包含所需列
        if "表名" not in df.columns or "问题说明" not in df.columns:
            logging.warning(f"DataFrame中未找到'表名'或'问题说明'列")
            return None
        
        # 查找匹配的行
        matching_rows = df[(df["表名"] == table_name) & (df["问题说明"] == problem_desc)].index
        if not matching_rows.empty:
            return matching_rows[0]
        
        # 如果未找到精确匹配，尝试部分匹配
        for idx, row in df.iterrows():
            if pd.notna(row.get("表名")) and table_name in str(row.get("表名")):
                if pd.notna(row.get("问题说明")) and problem_desc in str(row.get("问题说明")):
                    logging.info(f"通过部分匹配找到行: 表名='{row.get('表名')}', 问题说明='{row.get('问题说明')}'")
                    return idx
        
        logging.warning(f"未找到匹配表名='{table_name}'和问题说明='{problem_desc}'的行")
        return None
    except Exception as e:
        logging.error(f"查找行索引时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return None

def initialize_output_excel(template_path, output_path):
    """
    初始化输出Excel，复制模板的结构和样式
    
    Args:
        template_path (str): 模板Excel文件路径
        output_path (str): 输出Excel文件路径
    
    Returns:
        dict: 包含所有sheet页DataFrame的字典，格式为{sheet_name: df}
    """
    try:
        logging.info("初始化输出Excel，复制模板结构...")
        
        # 如果输出文件已存在，先删除它
        if os.path.exists(output_path):
            try:
                os.remove(output_path)
            except PermissionError:
                logging.warning(f"无法删除已存在的文件，可能被其他程序占用: {output_path}")
                # 尝试使用不同的文件名
                base, ext = os.path.splitext(output_path)
                output_path = f"{base}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}{ext}"
                logging.info(f"将使用新的输出路径: {output_path}")
        
        # 直接复制模板文件到输出路径
        shutil.copy2(template_path, output_path)
        
        # 读取所有sheet页到DataFrame字典，保持原始格式
        all_sheets = {}
        excel = pd.ExcelFile(output_path)
        sheet_names = excel.sheet_names
        
        for sheet_name in sheet_names:
            df = pd.read_excel(excel, sheet_name=sheet_name, header=0)
            all_sheets[sheet_name] = df
            logging.info(f"读取工作表 '{sheet_name}', 大小: {df.shape}")
        
        logging.info(f"成功初始化输出Excel，共 {len(all_sheets)} 个工作表")
        return all_sheets
    except Exception as e:
        logging.error(f"初始化输出Excel时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return {}

def find_hospital_column(df, hospital_name):
    """
    在DataFrame中查找医院名称对应的"质控结果"列索引
    
    Args:
        df (pandas.DataFrame): 工作表数据
        hospital_name (str): 医院名称
    
    Returns:
        tuple: (列名, 列索引)，如果未找到则返回(None, None)
    """
    try:
        # 检查第一行（表头行）中是否有完全匹配的医院名称
        for col_idx, col_name in enumerate(df.columns):
            if isinstance(col_name, str) and col_name.strip() == hospital_name:
                # 确保有下一列作为"质控结果"列
                if col_idx + 1 < len(df.columns):
                    result_col_name = df.columns[col_idx + 1]
                    result_col_idx = col_idx + 1
                    logging.debug(f"找到医院'{hospital_name}'的质控结果列: '{result_col_name}'")
                    return result_col_name, result_col_idx
        
        # 如果没有精确匹配，尝试部分匹配
        for col_idx, col_name in enumerate(df.columns):
            if isinstance(col_name, str) and hospital_name in col_name:
                # 确保有下一列作为"质控结果"列
                if col_idx + 1 < len(df.columns):
                    result_col_name = df.columns[col_idx + 1]
                    logging.debug(f"通过部分匹配找到医院'{hospital_name}'的质控结果列: '{result_col_name}'")
                    return result_col_name, col_idx + 1
        
        # 如果还是找不到，检查每个列名是否是Unnamed，前面一列是否包含医院名称
        for col_idx, col_name in enumerate(df.columns):
            if col_idx > 0 and isinstance(df.columns[col_idx-1], str):
                prev_col = df.columns[col_idx-1]
                if hospital_name in prev_col and "Unnamed:" in str(col_name):
                    logging.debug(f"通过检查前一列找到医院'{hospital_name}'的质控结果列: '{col_name}'")
                    return col_name, col_idx
        
        logging.warning(f"未找到医院'{hospital_name}'对应的质控结果列")
        return None, None
    except Exception as e:
        logging.error(f"查找医院列时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return None, None

def process_json_file(json_file_path, all_sheets, hospital_to_area):
    """
    处理单个JSON文件，填充数据到相应的Excel工作表
    
    Args:
        json_file_path (str): JSON文件路径
        all_sheets (dict): 包含所有工作表DataFrame的字典
        hospital_to_area (dict): 医院名称到行政区划的映射
    
    Returns:
        dict: 更新后的工作表DataFrame字典
        int: 成功填充的数据条数
    """
    try:
        logging.info(f"开始处理JSON文件: {os.path.basename(json_file_path)}")
        
        # 提取表名
        table_name = extract_table_name(os.path.basename(json_file_path))
        if not table_name:
            logging.error(f"无法从文件名 {json_file_path} 提取表名")
            return all_sheets, 0
        
        logging.info(f"提取的表名: {table_name}")
        
        # 加载JSON数据
        json_rows, column_names = load_json_data(json_file_path)
        if not json_rows:
            logging.warning(f"文件 {json_file_path} 中没有有效的数据行")
            return all_sheets, 0
        
        # 计数器
        fill_count = 0
        
        # 按医院分组处理数据
        for json_row in json_rows:
            hospital_name = json_row.get('hosname')
            if not hospital_name:
                continue
                
            # 查找医院对应的行政区划
            area = hospital_to_area.get(hospital_name)
            if not area:
                logging.warning(f"未找到医院 '{hospital_name}' 对应的行政区划")
                continue
                
            # 检查是否存在对应的行政区划工作表
            if area not in all_sheets:
                logging.warning(f"未找到行政区划 '{area}' 对应的工作表")
                continue
                
            # 获取对应行政区划的DataFrame
            area_df = all_sheets[area]
            
            # 在该区域的DataFrame中逐个处理JSON数据字段
            for field, value in json_row.items():
                # 跳过医院名称字段
                if field == 'hosname':
                    continue
                    
                # 查找匹配表名和问题说明（字段名）的行
                row_idx = find_row_index_by_table_and_problem(area_df, table_name, field)
                if row_idx is None:
                    continue
                    
                # 查找医院名称对应的"质控结果"列
                result_col_name, result_col_idx = find_hospital_column(area_df, hospital_name)
                if result_col_name is None:
                    continue
                
                # 填充数据
                area_df.iloc[row_idx, result_col_idx] = value
                logging.debug(f"已填充数据: 表名={table_name}, 问题={field}, 医院={hospital_name}, 值={value}")
                fill_count += 1
            
            # 更新字典中的DataFrame
            all_sheets[area] = area_df
            
        logging.info(f"文件 {os.path.basename(json_file_path)} 处理完成，成功填充 {fill_count} 条数据")
        return all_sheets, fill_count
    except Exception as e:
        logging.error(f"处理JSON文件 {json_file_path} 时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return all_sheets, 0

def save_all_sheets(all_sheets, output_path):
    """
    将所有工作表保存到Excel文件，保留合并单元格
    
    Args:
        all_sheets (dict): 包含所有工作表DataFrame的字典
        output_path (str): 输出Excel文件路径
    
    Returns:
        bool: 操作是否成功
    """
    try:
        logging.info(f"开始保存所有工作表到 {output_path}...")
        
        # 加载原始文件以保留合并单元格信息
        original_wb = load_workbook(output_path)
        
        # 使用ExcelWriter保存所有工作表，使用if_sheet_exists="replace"来替换现有工作表内容但保留格式
        with pd.ExcelWriter(output_path, engine='openpyxl', mode='a', if_sheet_exists="replace") as writer:
            # 设置工作簿，这样可以保留原始的合并单元格信息
            writer.book = original_wb
            writer.sheets = {ws.title: ws for ws in original_wb.worksheets}
            
            # 按顺序写入所有工作表
            for sheet_name, df in all_sheets.items():
                if sheet_name in writer.sheets:
                    # 清除现有单元格值但保留合并单元格结构
                    ws = writer.sheets[sheet_name]
                    # 保留第一行（表头行）
                    for row in ws.iter_rows(min_row=2):
                        for cell in row:
                            cell.value = None
                    
                    # 将DataFrame数据写入工作表，从第2行开始（保留表头）
                    start_row = 1
                    for r_idx, row in enumerate(dataframe_to_rows(df, index=False, header=False), start_row + 1):
                        for c_idx, value in enumerate(row, 1):
                            # 仅当值不为NaN时才写入
                            if pd.notna(value):
                                ws.cell(row=r_idx, column=c_idx, value=value)
                else:
                    # 如果工作表不存在，则创建新工作表
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                logging.info(f"已保存工作表 '{sheet_name}'")
        
        logging.info(f"所有工作表已成功保存到 {output_path}")
        return True
    except Exception as e:
        logging.error(f"保存Excel文件时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return False

def main():
    """
    主函数，协调整个处理流程
    """
    try:
        start_time = datetime.datetime.now()
        logging.info("=== 开始处理质控问题清单模板JSON转Excel ===")
        logging.info(f"Excel模板文件: {TEMPLATE_PATH}")
        logging.info(f"JSON数据目录: {JSON_DIR}")
        logging.info(f"输出文件路径: {OUTPUT_PATH}")
        
        # 第1步: 获取医院与行政区划的映射关系
        hospital_to_area = get_hospital_area_mapping(TEMPLATE_PATH)
        if not hospital_to_area:
            logging.error("无法获取医院与行政区划的映射关系，程序终止")
            return False
        
        # 第2步: 初始化输出Excel
        all_sheets = initialize_output_excel(TEMPLATE_PATH, OUTPUT_PATH)
        if not all_sheets:
            logging.error("初始化输出Excel失败，程序终止")
            return False
        
        # 第3步: 获取所有txt文件路径
        txt_files = []
        try:
            txt_files = [os.path.join(JSON_DIR, f) for f in os.listdir(JSON_DIR) 
                        if f.endswith('.txt') and f.startswith('coreflddq_')]
            logging.info(f"找到 {len(txt_files)} 个txt文件")
        except Exception as e:
            logging.error(f"读取JSON目录失败: {str(e)}")
            return False
        
        if not txt_files:
            logging.warning(f"在 {JSON_DIR} 中未找到符合条件的txt文件")
            return False
        
        # 第4步: 处理所有txt文件
        total_fill_count = 0
        for json_file_path in txt_files:
            all_sheets, fill_count = process_json_file(json_file_path, all_sheets, hospital_to_area)
            total_fill_count += fill_count
        
        logging.info(f"所有文件处理完成，共填充 {total_fill_count} 条数据")
        
        # 第5步: 保存结果
        if save_all_sheets(all_sheets, OUTPUT_PATH):
            end_time = datetime.datetime.now()
            elapsed_time = end_time - start_time
            logging.info(f"数据已成功保存到 {OUTPUT_PATH}")
            logging.info(f"总耗时: {elapsed_time}")
            return True
        else:
            logging.error("保存结果失败")
            return False
            
    except Exception as e:
        logging.error(f"程序执行时出错: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return False
    finally:
        logging.info("=== 处理结束 ===")

if __name__ == "__main__":
    main() 