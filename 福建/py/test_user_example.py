from mysql转其他库 import translate_sql

# 用户提供的原始 SQL
user_sql = """SELECT uscid AS uscid,'patient_allergy_record' AS tabname,date_format(rec_time,'%Y%m') AS ny,count(*) AS d_count 
FROM patient_allergy_record 
WHERE rec_time >= '2020-01-01 00:00:00' AND rec_time <= '2025-05-31 23:59:59' 
AND deleted = '0' 
GROUP BY uscid,date_format(rec_time,'%Y%m') 
ORDER BY date_format(rec_time,'%Y%m')"""

# 转换为 Oracle
oracle_result = translate_sql(user_sql, 'oracle')
print("\n===== 转换为 Oracle =====")
print(oracle_result)

# 用户提供的正确 Oracle SQL
correct_oracle = """SELECT 
    uscid AS uscid,
    'patient_allergy_record' AS tabname,
    TO_CHAR(rec_time, 'YYYYMM') AS ny,
    COUNT(*) AS d_count 
FROM 
    patient_allergy_record 
WHERE 
    rec_time >= TO_DATE('2020-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') 
    AND rec_time <= TO_DATE('2025-05-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS') 
    AND deleted = '0' 
GROUP BY 
    uscid, 
    TO_CHAR(rec_time, 'YYYYMM') 
ORDER BY 
    TO_CHAR(rec_time, 'YYYYMM')"""

print("\n===== 用户提供的正确 Oracle SQL =====")
print(correct_oracle)

# 检查转换结果是否正确
print("\n===== 验证转换结果 =====")
# 移除所有空白字符以进行比较
oracle_result_normalized = ''.join(oracle_result.split())
correct_oracle_normalized = ''.join(correct_oracle.split())

if oracle_result_normalized == correct_oracle_normalized:
    print("转换成功！结果与用户提供的正确 SQL 语义上一致。")
else:
    print("转换结果与用户提供的正确 SQL 语义上不一致。")
    print("\n主要区别:")
    print("1. 格式化差异 (空格、换行等)")
    print("2. 语义上是否等价 (TO_DATE 函数、日期格式等)")
    
    # 检查关键点
    if "TO_DATE('2020-01-01 00:00:00'" in oracle_result and "TO_DATE('2025-05-31 23:59:59'" in oracle_result:
        print("\n✓ 日期字面量已正确转换为 TO_DATE 函数")
    else:
        print("\n✗ 日期字面量未正确转换为 TO_DATE 函数")
        
    if "TO_CHAR(rec_time, 'YYYYMM')" in oracle_result:
        print("✓ DATE_FORMAT 函数已正确转换为 TO_CHAR 函数")
    else:
        print("✗ DATE_FORMAT 函数未正确转换为 TO_CHAR 函数") 