from mysql转其他库 import translate_sql

# 测试GaussDB正则表达式转换
test_sql = "SELECT COUNT(1) FROM patient_allergy_record A where 1=1 and A.SKINTST_MEDSTFFNAME REGEXP '[^a-zA-Z]'"
result = translate_sql(test_sql, 'gaussdb')
print("原始SQL:", test_sql)
print("转换后:", result)

# 测试复杂的正则表达式
complex_sql = """
SELECT 'YD01202506171810','患者过敏记录中的皮试人员姓名不为空时，只能包含汉字、英文、点',COUNT(1) 
FROM patient_allergy_record A 
where 1=1 
and A.SKINTST_MEDSTFFNAME REGEXP '[^a-zA-Z\\u4e00-\\u9fa5·]' 
OR A.SKINTST_MEDSTFFNAME LIKE '·%' 
OR A.SKINTST_MEDSTFFNAME LIKE '%·' 
OR (A.SKINTST_MEDSTFFNAME REGEXP '^[\\u4e00-\\u9fa5]+$' AND A.SKINTST_MEDSTFFNAME LIKE '% %')
"""
complex_result = translate_sql(complex_sql, 'gaussdb')
print("\n原始复杂SQL:", complex_sql)
print("转换后:", complex_result)

# 测试not REGEXP形式
not_regexp_sql = "SELECT COUNT(1) FROM patient_allergy_record A where 1=1 and A.SKINTST_MEDSTFFNAME not REGEXP '[a-zA-Z]'"
not_regexp_result = translate_sql(not_regexp_sql, 'gaussdb')
print("\n原始not REGEXP SQL:", not_regexp_sql)
print("转换后:", not_regexp_result)

# 测试REGEXP = 0形式
regexp_not_equal_sql = "SELECT COUNT(1) FROM patient_allergy_record A where 1=1 and A.SKINTST_MEDSTFFNAME REGEXP '[a-zA-Z]' = 0"
regexp_not_equal_result = translate_sql(regexp_not_equal_sql, 'gaussdb')
print("\n原始REGEXP = 0 SQL:", regexp_not_equal_sql)
print("转换后:", regexp_not_equal_result) 