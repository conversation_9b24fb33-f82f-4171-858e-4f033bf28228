# -*- coding: utf-8 -*-
"""
Created on Thu Jan 16 11:48:15 2025

@author: lidan
"""

# -*- coding: utf-8 -*-
"""
Created on Fri Jun  5 11:45:52 2020

@author: Administrator

"""

from datetime import date
import os
import win32com.client
import time

import docx
from docx.enum.table import WD_TABLE_ALIGNMENT,WD_CELL_VERTICAL_ALIGNMENT
from docx.enum.text import WD_ALIGN_PARAGRAPH,WD_PARAGRAPH_ALIGNMENT

from docx import Document

import pandas as pd

from docx.oxml.ns import qn
from docx.shared import Pt,Cm,Inches
from copy import deepcopy
import lxml
from docx.oxml import OxmlElement

file_path = r'D:\work\demo\福建'
template_path = os.path.join(file_path, '模板')
output_base_path = os.path.join(file_path, '生成的word')

excel_name='三医数据采集标准梳理.xlsx'

# 读取Excel数据
data=pd.read_excel(file_path+"""\\"""+excel_name)
data=data.fillna('')
data=data[data['数据项']!='']

file_names = list(data['文件名称'].drop_duplicates())

def find_template_file(template_name):
    """在模板目录及其子目录中查找模板文件"""
    for root, dirs, files in os.walk(template_path):
        if template_name in files:
            return os.path.join(root, template_name)
    return None

def ensure_output_dir(template_file_path):
    """确保输出目录结构与模板目录结构相同"""
    # 获取相对路径
    rel_path = os.path.relpath(os.path.dirname(template_file_path), template_path)
    # 创建对应的输出目录
    output_dir = os.path.join(output_base_path, rel_path)
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def update_toc(doc_path):
    """更新Word文档的目录"""
    word = win32com.client.DispatchEx('Word.Application')
    try:
        word.Visible = False
        doc = word.Documents.Open(doc_path)
        doc.TablesOfContents(1).Update()
        doc.Close(SaveChanges=True)
    except Exception as e:
        print(f"更新目录时出错: {str(e)}")
    finally:
        word.Quit()

for file_name in file_names:# 一个文件一个文件生成
    # 查找模板文件
    template_file = find_template_file(file_name)
    if not template_file:
        print(f'警告：未找到模板文件 {file_name}')
        continue

    data_in=data[data['文件名称']==file_name]  # 分册数据
    uniques = list(data_in['表全名'].drop_duplicates())  # 表全名去重

    document = docx.Document(template_file)  # 打开模板文档

    # 获取所有表格
    all_tables = document.tables
    # 保留最后一个表格作为模板
    tables = [all_tables[-1]] if all_tables else []

    for i in range(len(uniques)):
        tb_name = uniques[i]
        if tb_name=='':
            print('警告---------------表全名有空')

        tb = data_in[data_in['表全名']==tb_name]
        tb = tb[['数据项', '字段名', '数据类型', '长度','填报要求', '是否主键','说明' ]]

        para_heading=document.add_heading('',level=2)#返回2级标题段落对象
        para_heading.add_run(tb_name)#这个使用的是"Heading 2" 的样式标题是2级目录

        print('开始写入',file_name,'----------',tb_name,'-----------',i,'/',len(uniques),'字段个数',len(tb))

        # 复制固定模板表格，后续用(该表格头的样式，尤其是分页加标题在)
        table_copy = deepcopy(tables[0])
        # 在文档的最后加入这张表格
        document.paragraphs[-1]._p.addnext(table_copy._tbl)

        for row_id in range(len(tb)):
            row = table_copy.add_row()  # 对最新的一个表格进行赋值
            for col in range(7):
                row.cells[col].text = str(tb.iloc[row_id,col])
                paragraph = row.cells[col].paragraphs[0]

                row.cells[col].vertical_alignment = WD_CELL_VERTICAL_ALIGNMENT.CENTER # 垂直对齐方式
                if col in [4,5]:  # 水平对齐
                    paragraph.paragraph_format.alignment = WD_TABLE_ALIGNMENT.CENTER
                if col in [0,1,2,3,6]:  # 左对齐
                    paragraph.paragraph_format.alignment = WD_TABLE_ALIGNMENT.LEFT

                # 设置字体
                if paragraph.runs:
                    paragraph.runs[0].font.size = Pt(9)
                    paragraph.runs[0].font.name = u'宋体'
                else:
                    run = paragraph.add_run()
                    run.font.size = Pt(9)
                    run.font.name = u'宋体'

    t = tables[0]._element # 获取模板表格元素
    t.getparent().remove(t) # 删除模板表格

    #以下自动更新目录
    namespace = "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}"
    element_updatefields = lxml.etree.SubElement(document.settings.element, namespace+"updateFields")
    element_updatefields.set(namespace+"val", "true")

    # 获取对应的输出目录
    output_dir = ensure_output_dir(template_file)
    # 保存文档到对应的子目录
    output_file = os.path.join(output_dir, file_name[:-5].replace('-','')+'.docx')
    document.save(output_file)
    
    # 更新文档目录
    abs_output_file = os.path.abspath(output_file)
    print(f'正在更新文档目录: {file_name}')
    update_toc(abs_output_file)
    print(f'文档目录更新完成: {file_name}')






