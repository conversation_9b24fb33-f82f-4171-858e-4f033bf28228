from mysql转其他库 import translate_sql

# 用户提供的原始SQL
user_sql = """
SELECT 'YD01202506171810','患者过敏记录（PATIENT_ALLERGY_RECORD）中的皮试人员姓名（SKINTST_MEDSTFFNAME）不为空时，只能包含汉字、英文、· ，首位和结尾不能是 · ，纯中文时不可含有空格',COUNT(1) 
FROM patient_allergy_record A 
where 1=1 
and A.SKINTST_MEDSTFFNAME REGEXP '[^a-zA-Z\\u4e00-\\u9fa5·]' 
OR A.SKINTST_MEDSTFFNAME LIKE '·%' 
OR A.SKINTST_MEDSTFFNAME LIKE '%·' 
OR (A.SKINTST_MEDSTFFNAME REGEXP '^[\\u4e00-\\u9fa5]+$' AND A.SKINTST_MEDSTFFNAME LIKE '% %')
"""

# 转换为GaussDB
gaussdb_sql = translate_sql(user_sql, 'gaussdb')
print("原始SQL:")
print(user_sql)
print("\nGaussDB转换结果:")
print(gaussdb_sql)

# 转换为Oracle
oracle_sql = translate_sql(user_sql, 'oracle')
print("\nOracle转换结果:")
print(oracle_sql)

# 转换为SQL Server
sqlserver_sql = translate_sql(user_sql, 'sqlserver')
print("\nSQL Server转换结果:")
print(sqlserver_sql) 