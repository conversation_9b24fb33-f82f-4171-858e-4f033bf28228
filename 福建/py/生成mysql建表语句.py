import pandas as pd
import os
import datetime

def get_mysql_type(data_type: str, length: str) -> str:
    """
    将Excel中的数据类型转换为MySQL数据类型

    Args:
        data_type: Excel中的数据类型
        length: 长度字段的值

    Returns:
        str: MySQL数据类型
    """
    data_type = str(data_type).lower().strip()
    length = str(length).strip()

    # 处理空值

    if pd.isna(data_type) or data_type == 'nan' or data_type == '':
        return 'VARCHAR(255)'

    # 处理整数类型
    if '整数' in data_type:
        return 'INT'

    # 处理数值类型（小数）
    if '数值' in data_type:
        if length and length != 'nan':
            try:
                if '.' in length:
                    total_len, decimal_len = length.split('.')
                    return f'DECIMAL({total_len},{decimal_len})'
                return f'DECIMAL({length})'  # 如果没有指定小数位，默认2位
            except ValueError:
                return 'DECIMAL(10,2)'  # 如果无法解析长度，使用默认值
        return 'DECIMAL(10,2)'  # 如果没有指定长度，使用默认值

    # 处理字符串类型
    if '字符' in data_type or '文本' in data_type:
        if length and length != 'nan':
            try:
                length_val = int(length)
                # 当字段长度超过500时，自动转换为TEXT类型以避免行大小限制
                if length_val > 500:
                    return 'TEXT'
                return f'VARCHAR({length})'
            except ValueError:
                return 'VARCHAR(255)'
        return 'VARCHAR(255)'

    # 处理日期时间类型
    if '日期' in data_type:
        # 纯日期类型使用DATE
        if '时间' not in data_type:
            return 'DATE'
        # 带时间的都使用DATETIME
        return 'DATETIME'

    # 处理时间类型（不含日期的情况）
    if '时间' in data_type:
        return 'DATETIME'

    # 默认返回VARCHAR(255)
    return 'VARCHAR(255)'

def generate_create_table_sql(df: pd.DataFrame) -> tuple[str, str]:
    """
    根据DataFrame生成建表SQL语句

    Args:
        df: 包含表结构的DataFrame

    Returns:
        tuple: (建表SQL语句, 表名)
    """
    # 获取表名
    table_name = df.iloc[0]['表名'].strip()
    table_comment = df.iloc[0]['表中文名'].strip()

    # SQL语句头部
    sql = f"CREATE TABLE IF NOT EXISTS `{table_name}` (\n"

    # 记录主键字段
    primary_keys = []
    # 新增：记录需要创建唯一索引的字段
    unique_index_fields = []

    # 添加字段定义
    for _, row in df.iterrows():
        field_name = row['字段名'].strip()
        data_type = get_mysql_type(row['数据类型'], row['长度'])
        is_primary = str(row['是否主键']).strip().upper()

        # 组合数据项和说明作为注释
        data_item = str(row.get('数据项', '')).strip()
        description = str(row['说明']).strip()
        comment_parts = []
        if data_item and data_item != 'nan':
            comment_parts.append(data_item)
        if description and description != 'nan':
            comment_parts.append(description)
        comment = ' '.join(comment_parts)

        nullable = '不能为空' in str(row['填报要求'])

        # 构建字段定义
        sql += f"    `{field_name}` {data_type}"

        # 添加是否可为空
        if nullable:
            sql += " NOT NULL"

        # 添加注释
        if comment:
            sql += f" COMMENT '{comment}'"

        sql += ",\n"

        # 记录主键
        if is_primary == '是' or is_primary == 'Y' or is_primary == '1':
            primary_keys.append(field_name)

        # 新增：检查说明中是否包含"唯一索引"
        if '唯一索引' in str(row['说明']):
            unique_index_fields.append(field_name)

    # 添加主键定义
    if primary_keys:
        # 使用简短的主键约束名：pk_表名缩写
        table_abbr = ''.join(word[0] for word in table_name.split('_'))[:5]  # 取表名每个单词首字母，最多5个字符
        sql += f"    CONSTRAINT `pk_{table_abbr}` PRIMARY KEY ({', '.join([f'`{pk}`' for pk in primary_keys])})"
        if unique_index_fields:
            sql += ",\n"
        else:
            sql += "\n"

    # 新增：添加唯一索引定义
    if unique_index_fields:
        sql += f"    UNIQUE INDEX `idx_{'_'.join(unique_index_fields)}` ({', '.join([f'`{field}`' for field in unique_index_fields])})\n"
    else:
        # 如果没有主键也没有唯一索引，删除最后一个逗号和换行符
        if not primary_keys:
            sql = sql.rstrip(",\n") + "\n"

    # 添加表注释和结束符
    sql += f") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='{table_comment}';\n\n"

    return sql, table_name

def main():
    """主函数"""
    # 设置文件路径
    base_path = r'D:\work\demo\福建'
    excel_path = os.path.join(base_path, '建表模板.xlsx')
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    output_path = os.path.join(base_path+'\建表sql', f"mysql_184_{current_date}.sql")


    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path)

        # 获取表名的顺序
        table_order = []
        seen_tables = set()
        for table_name in df['表名']:
            if table_name not in seen_tables:
                table_order.append(table_name)
                seen_tables.add(table_name)

        # 按Excel中的顺序生成建表语句
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 写入SQL文件头部
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f'-- 生成时间：{current_time}\n')
            f.write('-- 此脚本包含错误处理逻辑，如果某个表创建失败，将继续执行后续表的创建\n\n')

            # 首先创建日志表，使用独立的存储过程
            f.write("""
-- 创建日志表的存储过程
DROP PROCEDURE IF EXISTS create_log_table;
DELIMITER //
CREATE PROCEDURE create_log_table()
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        -- 如果日志表创建失败，输出错误信息
        GET DIAGNOSTICS CONDITION 1 @err_msg = MESSAGE_TEXT;
        SELECT CONCAT('错误：创建日志表失败 - ', @err_msg) AS message;
    END;

    CREATE TABLE IF NOT EXISTS `table_creation_log` (
        `id` INT AUTO_INCREMENT PRIMARY KEY,
        `table_name` VARCHAR(255) NOT NULL COMMENT '表名',
        `status` VARCHAR(10) NOT NULL COMMENT '状态：成功/失败',
        `message` TEXT COMMENT '详细信息',
        `error_code` VARCHAR(10) COMMENT '错误代码',
        `create_time` DATETIME NOT NULL COMMENT '创建时间'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表创建结果记录';
    
    -- 清空记录表
    TRUNCATE TABLE `table_creation_log`;
END //
DELIMITER ;

-- 创建日志表
CALL create_log_table();
DROP PROCEDURE IF EXISTS create_log_table;\n\n""")

            # 为每个表添加错误处理
            for table_name in table_order:
                print(f"正在生成表 {table_name} 的建表语句")
                group = df[df['表名'] == table_name]
                sql, table_name = generate_create_table_sql(group)

                # 创建存储过程
                proc_name = f"create_{table_name}_table"
                f.write(f"DROP PROCEDURE IF EXISTS {proc_name};\n")
                f.write("DELIMITER //\n")
                f.write(f"CREATE PROCEDURE {proc_name}()\n")
                f.write("BEGIN\n")
                f.write("    DECLARE success BOOLEAN DEFAULT TRUE;\n")
                f.write("    DECLARE error_msg TEXT;\n")
                f.write("    DECLARE error_code VARCHAR(10);\n")
                f.write("    DECLARE EXIT HANDLER FOR SQLEXCEPTION\n")  # 改为 EXIT HANDLER
                f.write("    BEGIN\n")
                f.write("        GET DIAGNOSTICS CONDITION 1\n")
                f.write("            error_msg = MESSAGE_TEXT,\n")
                f.write("            error_code = MYSQL_ERRNO;\n")
                f.write("""
        INSERT INTO `table_creation_log` 
            (`table_name`, `status`, `message`, `error_code`, `create_time`)
        VALUES (
            '""" + table_name + """',
            '失败',
            error_msg,
            error_code,
            NOW()
        );\n""")
                # f.write(f"        SELECT CONCAT('错误：创建表 {table_name} 失败 - ', error_msg, ' (错误代码: ', error_code, ')') AS message;\n")
                f.write("    END;\n\n")

                # 写入建表语句
                f.write("    " + sql.replace('\n', '\n    '))

                # 记录成功结果
                f.write("""
    INSERT INTO `table_creation_log` 
        (`table_name`, `status`, `message`, `error_code`, `create_time`)
    VALUES (
        '""" + table_name + """',
        '成功',
        '表创建成功',
        NULL,
        NOW()
    );
    \n""")

                f.write("END //\n")
                f.write("DELIMITER ;\n\n")

                # 调用存储过程
                f.write(f"CALL {proc_name}();\n")
                f.write(f"DROP PROCEDURE IF EXISTS {proc_name};\n\n")

            # 添加最终结果统计查询
            f.write("""
-- 显示执行结果统计
SELECT 
    COUNT(*) as total_tables,
    SUM(CASE WHEN status = '成功' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN status = '失败' THEN 1 ELSE 0 END) as failed_count
FROM `table_creation_log`;\n\n""")

            # 添加失败表详情查询，包含错误代码
            f.write("""
-- 显示失败的表详情
SELECT 
    table_name, 
    message, 
    error_code,
    create_time 
FROM `table_creation_log` 
WHERE status = '失败' 
ORDER BY create_time;\n""")

        print(f"\n成功生成SQL文件: {output_path}")
        print(f"共生成 {len(table_order)} 个表的建表语句")
        print("请注意：每个表的创建都包含了错误处理逻辑，如果某个表创建失败，脚本会继续执行后续表的创建")
        print("所有表的创建结果都会记录在 table_creation_log 表中，包含具体的错误代码和错误信息")

    except Exception as e:
        print(f"生成脚本时发生错误: {str(e)}")

if __name__ == "__main__":
    main()
