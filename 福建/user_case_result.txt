原始 SQL: SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL REGEXP '^1[3456789]\d{9}$' = 0

Oracle 11g: SELECT count(*) FROM patient_basic_info A where 1=1 and NOT REGEXP_LIKE(A.EMPR_TEL, '^1[3456789]\d{9}$')

SQL Server 2017: SELECT count(*) FROM patient_basic_info A where 1=1 and A.EMPR_TEL REGEXP '^1[3456789]\d{9}$' = 0

GaussDB 5.0: SELECT count(*) FROM patient_basic_info A where 1=1 and <PERSON><PERSON>EMPR_TEL REGEXP '^1[3456789]\d{9}$' = 0
