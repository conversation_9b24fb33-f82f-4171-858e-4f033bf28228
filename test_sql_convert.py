from 福建.py.mysql转其他库 import translate_sql

# 测试SQL语句
test_sql = "SELECT * FROM patient_basic_info A WHERE A.coner_cert_no REGEXP '^[0-9]{17}[0-9X]$' = 0"
print("原始SQL:", test_sql)
print("\nSQL Server转换结果:", translate_sql(test_sql, 'sqlserver'))
print("\nOracle转换结果:", translate_sql(test_sql, 'oracle'))

# 测试用户提供的完整SQL
user_sql = """SELECT 'YD01202506175127' AS 规则编码, '患者基本信息表（PATIENT_BASIC_INFO）中的联系人/监护人身份证件号码（CONER_CERT_NO）格式错误' AS 问题描述 ,count(*) AS 问题数据量  FROM patient_basic_info A WHERE 1=1 AND LEN(<PERSON>.coner_cert_no) <> 15 AND LEN(<PERSON><PERSON>coner_cert_no) <> 18 OR <PERSON>.coner_cert_no REGEXP '^[0-9]{17}[0-9X]$' = 0 OR SUBSTRING(A.coner_cert_no, 1, 2) NOT IN ('11', '12', '13', '14', '15', '21', '22', '23', '31', '32', '33', '34', '35', '36', '37', '41', '42', '43', '44', '45', '46', '50', '51', '52', '53', '54', '61', '62', '63', '64', '65')"""

print("\n\n用户SQL:", user_sql)
print("\nSQL Server转换结果:", translate_sql(user_sql, 'sqlserver'))
print("\nOracle转换结果:", translate_sql(user_sql, 'oracle')) 