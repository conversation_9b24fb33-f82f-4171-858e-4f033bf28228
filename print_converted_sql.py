from 福建.py.mysql转其他库 import translate_sql

# 用户提供的SQL
user_sql = """SELECT 'YD01202506175127' AS 规则编码, '患者基本信息表（PATIENT_BASIC_INFO）中的联系人/监护人身份证件号码（CONER_CERT_NO）格式错误' AS 问题描述 ,count(*) AS 问题数据量  FROM patient_basic_info A WHERE 1=1 AND LEN(A.coner_cert_no) <> 15 AND LEN(A.coner_cert_no) <> 18 OR A.coner_cert_no REGEXP '^[0-9]{17}[0-9X]$' = 0 OR SUBSTRING(A.coner_cert_no, 1, 2) NOT IN ('11', '12', '13', '14', '15', '21', '22', '23', '31', '32', '33', '34', '35', '36', '37', '41', '42', '43', '44', '45', '46', '50', '51', '52', '53', '54', '61', '62', '63', '64', '65')"""

# 转换为各种方言
oracle_sql = translate_sql(user_sql, 'oracle')
sqlserver_sql = translate_sql(user_sql, 'sqlserver')
gaussdb_sql = translate_sql(user_sql, 'gaussdb')

print("=== 原始 SQL ===")
print(user_sql)
print("\n=== Oracle 11g ===")
print(oracle_sql)
print("\n=== SQL Server 2017 ===")
print(sqlserver_sql)
print("\n=== GaussDB 5.0 ===")
print(gaussdb_sql)

print("\n\n=== 转换说明 ===")
print("该 SQL 包含正则表达式 (REGEXP) 操作")

if 'REGEXP_LIKE' in oracle_sql:
    print("\nOracle: 已转换为 REGEXP_LIKE 函数，支持完整的正则表达式功能")

if '/* 身份证号码验证 */' in sqlserver_sql:
    print("\nSQL Server: 已识别到身份证号码验证模式，转换为特定的验证逻辑")
    print("- 使用 LEN() 函数检查长度是否为18位")
    print("- 使用 LIKE 模式匹配检查格式是否符合身份证号码规范")
    print("- 已转换为 SQL Server 兼容的语法，可以直接执行")

if 'REGEXP' in gaussdb_sql:
    print("\nGaussDB: 保留原始 REGEXP 语法，GaussDB 支持类似 MySQL 的正则表达式") 