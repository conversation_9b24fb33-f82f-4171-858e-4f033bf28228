from PIL import Image
import os
import math

folder_path = r'D:\小宝照片\备选'  # 图片文件夹路径
output_path = r'D:\小宝照片\合成'  # 输出路径

# 获取文件夹中的所有图片
image_files = [f for f in os.listdir(folder_path) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]

# 如果没有找到图片，则退出程序
if len(image_files) == 0:
    print("No images found in the folder.")
    exit()

# 读取图片
images = []
for img_file in image_files:
    img_path = os.path.join(folder_path, img_file)
    img = Image.open(img_path)
    images.append(img)

# 计算合适的行数和列数
num_images = len(images)
columns = math.ceil(math.sqrt(num_images))  # 列数（接近正方形）
rows = math.ceil(num_images / columns)  # 行数

# 计算每张图片的最大宽度和最大高度，并缩小50倍
max_width = max(img.width for img in images) // 50
max_height = max(img.height for img in images) // 50

# 创建合成图的大小
total_width = columns * max_width
total_height = rows * max_height

# 创建一个空白的图像，背景为白色
new_image = Image.new('RGB', (total_width, total_height), (255, 255, 255))

# 将图片按行列拼接
current_x = 0
current_y = 0
for i, img in enumerate(images):
    # 缩小图片
    img_resized = img.resize((max_width, max_height))

    # 将缩小后的图片粘贴到合成图上
    new_image.paste(img_resized, (current_x, current_y))

    # 计算下一张图片的位置
    current_x += max_width
    if (i + 1) % columns == 0:
        current_x = 0
        current_y += max_height

# 保存合成后的图片
new_image.save(os.path.join(output_path, '合成图片.png'))

print(f"图片合成完成，已保存到 '{os.path.join(output_path, '合成图片.png')}'")
