package com.lightfly.softsecret.chenlu;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/3/10 23:07:18
 */
public class GuaHaoTest {

    private static String cookie = "gray-active=true; providerId=wechat; token=rd_1_z-9z5O1Q87puCJgAA; userId=A7A1B68E2BF64D82ADFC5577A840DEC3; wechat_access_token=90_LsLMt6tKf2_Qmzjpre9xb0tmNBzj_xDbbUR2BMIz1B8oQsGkVy2y_JT1BLw-bWv2Svtmh91gsVaEANwtA_f6eCEct-eYvCv7AcfZlDgUvtU; wechat_openId=ovTEjt6O3YT3ljWO7Qwgu-hYKspw; Hm_lpvt_4f5e6d65812072c49089f068396b8513=**********; Hm_lvt_4f5e6d65812072c49089f068396b8513=**********,**********; HMACCOUNT=14EF6E8829B4D21E";

    // 域名
    private static String domain = "https://mp.mhealth100.com/";

    // 医院代号  辽宁省沈阳市皇姑区北陵大街33号:**********  辽宁省本溪市溪湖区本溪市溪湖区枫叶路189号:**********
    private static String branchCode = "**********";
    // 两个医院的名字
    private static String branchName = "辽宁中医药大学附属医院";

    // 部门信息,010是儿科
    private static String deptId = "010";
    private static String deptName = "儿科门诊";

    // 医生id,宋铁玎:204
    private static String doctorId = "222"; // 测试,赵历军

    // 查询号源日期范围
    private static String startDate = DateUtil.now();
    private static String endDate = "2025-03-31";

    // 获取医生排班信息
    private static String getDoctorSchedule(JSONObject payInfo, JSONObject costInfo, JSONObject card, String branchCode, String deptId, String deptName, String doctorId, String startDate, String endDate) throws InterruptedException {
        // 使用CountDownLatch,阻塞主线程
        CountDownLatch ct = new CountDownLatch(1);
        // 全部日期的排班信息
        String url = domain + "/gateway/registration/appointment/scheduleNew/find?" +
                "branchCode=" + branchCode + "" +
                "&deptId=" + deptId + "" +
                "&deptName=" + URLUtil.encode(deptName) + "" +
                "&deptType=" +
                "&startDate=" + startDate + "" +
                "&endDate=" + endDate + "" +
                "&ajaxConfig=true";
        String body = HttpRequest.get(url).cookie(cookie).execute().body();
        List<JSONObject> regInfos = JSONUtil.parseObj(body).getJSONObject("data").getJSONArray("regInfos").toList(JSONObject.class);

        // 根据医生id过滤,获取当前医生所有排班信息
        JSONObject doctorInfo = regInfos.stream().filter(regInfo -> regInfo.getStr("doctorId").equals(doctorId)).collect(Collectors.toList()).get(0);
        // 打印当前医生信息:doctorId,doctorName,doctorTitle,doctorLevelCode
        String doctorName = doctorInfo.getStr("doctorName");
        String doctorTitle = doctorInfo.getStr("doctorTitle");
        String doctorLevelCode = doctorInfo.getStr("doctorLevelCode");
        System.out.printf("医生信息: doctorId: %s, doctorName: %s, doctorTitle: %s, doctorLevelCode: %s%n", doctorId, doctorName, doctorTitle, doctorLevelCode);

        // 当前医生排班信息,scheduleInfos
        List<JSONObject> scheduleInfos = doctorInfo.getJSONArray("scheduleInfos").toList(JSONObject.class);
        System.out.println("当前医生排班信息:");
        for (JSONObject scheduleInfo : scheduleInfos) {
            // 排班id
            String scheduleId = scheduleInfo.getStr("scheduleId");
            String treatFee = scheduleInfo.getStr("treatFee");
            String regFee = scheduleInfo.getStr("regFee");
            // 出诊日期
            String regDate = scheduleInfo.getStr("regDate");
            // 1:上午 2:下午
            String timeFlag = scheduleInfo.getStr("timeFlag");
            String shiftName = scheduleInfo.getStr("shiftName");
            // 开始出诊时间
            String startTime = scheduleInfo.getStr("startTime");
            // 结束出诊时间
            String endTime = scheduleInfo.getStr("endTime");

            // 总号源数量
            int regTotalCount = scheduleInfo.getInt("regTotalCount");
            // 号源剩余数量
            int regLeaveCount = scheduleInfo.getInt("regLeaveCount");
            // 出诊星期
            String weekName = scheduleInfo.getStr("weekName");
            System.out.println("    scheduleId: " + scheduleId + ", treatFee:" + treatFee + " regFee: " + regFee + ", regDate: " + regDate + ", timeFlag: " + timeFlag + ", shiftName: " + shiftName + ", startTime: " + startTime + ", endTime: " + endTime + ", regTotalCount: " + regTotalCount + ", regLeaveCount: " + regLeaveCount + ", weekName: " + weekName);

            // regLeaveCount>0可以获取具体号源时间段信息
            if (regLeaveCount > 0) {
                List<JSONObject> doctorTimeFind = doctorTimeFind(branchCode, deptId, deptName, doctorId, regDate, scheduleId, timeFlag);
                for (JSONObject doctorTimeInfo : doctorTimeFind) {
                    String dtPeriodId = doctorTimeInfo.getStr("periodId");
                    String dtStartTime = doctorTimeInfo.getStr("startTime");
                    String dtEndTime = doctorTimeInfo.getStr("endTime");
                    int dtRegTotalCount = doctorTimeInfo.getInt("regTotalCount");
                    int dtRegLeaveCount = doctorTimeInfo.getInt("regLeaveCount");
                    String dtScheduleId = doctorTimeInfo.getStr("scheduleId");
                    String dtSequence = doctorTimeInfo.getStr("sequence");
                    System.out.printf("    scheduleId: %s, 时间段号源信息:periodId: %s, startTime: %s, endTime: %s, 总号源数量: %d, 剩余号源数量: %d, scheduleId: %s, sequence: %s%n",
                            scheduleId, dtPeriodId, dtStartTime, dtEndTime, dtRegTotalCount, dtRegLeaveCount, dtScheduleId, dtSequence);

                    // dtRegLeaveCount > 0可以下单
                    if (dtRegLeaveCount > 0) {
                        scheduleInfo.putOpt("queueSn", dtPeriodId);
                        scheduleInfo.putOpt("periodId", dtPeriodId);

                        scheduleInfo.putOpt("startTime", dtStartTime);
                        scheduleInfo.putOpt("endTime", dtEndTime);

                        System.out.println("当前时间段有号源,尝试下一次单");
                        // 实际使用的时候这里new Thread,异步下单10次,每次间隔1秒
                        new Thread(() -> {
                            orderCreateAsync(doctorInfo, scheduleInfo, payInfo, costInfo, card, 10, 1);
                        }).start();
                    }

                }
            }


        }
        ct.await();
        return null;
    }

    /**
     * 某个医生某天具体时间段的号源信息
     */
    private static List<JSONObject> doctorTimeFind(String branchCode, String deptId, String deptName, String doctorId, String regDate, String scheduleId, String timeFlag) {
        String url = domain + "/gateway/registration/doctor/time/find?" +
                "branchCode=" + branchCode + "" +
                "&deptName=" + URLUtil.encode(deptName) + "" +
                "&deptId=" + deptId + "" +
                "&doctorId=" + doctorId + "" +
                "&regDate=" + regDate + "" +
                "&scheduleId=" + scheduleId + "" +
                "&timeFlag=" + timeFlag + "" +
                "&ajaxConfig=true";
        String body = HttpRequest.get(url).cookie(cookie).execute().body();
        return JSONUtil.parseObj(body).getJSONArray("data").toList(JSONObject.class);
    }

    /**
     * 获取就诊卡信息
     *
     * @param name 姓名
     */
    private static JSONObject findCard(String name) {
        String url = domain + "/gateway/patient/healthcard/api/findCards";
        String body = HttpRequest.get(url).cookie(cookie).execute().body();
        List<JSONObject> data = JSONUtil.parseObj(body).getJSONArray("data").toList(JSONObject.class);
        for (JSONObject datum : data) {
            if (name.equals(datum.getStr("patName"))) {
                return datum;
            }
        }
        return null;
    }

    /**
     * 支付方式
     * 01 一般自费__2小时内支付否则自动取消
     * 05 市医保__无需支付,现场持医保卡支付
     * 06 省医保__无需支付,现场持医保卡支付
     * 99 其它类别__无需支付,现场持医保卡或干诊卡支付
     */
    private static JSONObject svObjectFind(String bindingId, String branchCode, String deptId, String svObjectId) {
        String url = domain + "gateway/registration/svobject/find?" +
                "bindingId=" + bindingId + "" +
                "&branchCode=" + branchCode + "" +
                "&deptId=" + deptId;
        String body = HttpRequest.get(url).cookie(cookie).execute().body();
        List<JSONObject> data = JSONUtil.parseObj(body).getJSONArray("data").toList(JSONObject.class);
        // 过滤svObjectId=01的支付方式,返回
        return data.stream()
                .filter(d -> svObjectId.equals(d.getStr("svObjectId")))
                .findFirst()
                .orElse(null);
    }

    /**
     * 下单时的cost信息
     */
    private static JSONObject costFind(String branchCode, String bindingId, String deptId, String deptName, String doctorId, String doctorName, String patientId, String regDate, String scheduleIdFromSchedule, String scheduleIdFromTimeInfo, String timeFlag, String svObjectId, String isInsuran, String diseaseId, String applyId) {
        String url = domain + "gateway/registration/cost/find?" +
                "branchCode=" + branchCode + "" +
                "&bindingId=" + bindingId + "" +
                "&deptId=" + deptId + "" +
                "&deptName=" + URLUtil.encode(deptName) + "" +
                "&doctorId=" + doctorId + "" +
                "&doctorName=" + URLUtil.encode(doctorName) + "" +
                "&patientId=" + patientId + "" +
                "&regDate=" + regDate + "" +
                "&scheduleIdFromSchedule=" + scheduleIdFromSchedule + "" +
                "&scheduleIdFromTimeInfo=" +
                "&timeFlag=" + timeFlag + "" +
                "&svObjectId=" + svObjectId + "" +
                "&isInsuran=" + isInsuran + "" +
                "&diseaseId=&applyId=";
        String body = HttpRequest.get(url).cookie(cookie).execute().body();
        return JSONUtil.parseObj(body).getJSONObject("data");
    }

    /**
     * 不用请求，返回写死
     */
    private static JSONObject costFind() {
        String str = "{\"data\":{\"yhFee\":\"0.0\",\"svObject\":\"市医保\",\"medicareSettleLogId\":\"\",\"cashFee\":\"0\",\"insuranFee\":\"\",\"regFee\":\"\",\"treatFee\":\"\",\"insuranceSelfFee\":\"\",\"insuranceFundFee\":\"\",\"insuranceOtherFee\":\"\",\"totalShowFee\":\"\",\"payShowFee\":\"\"},\"resultCode\":\"0\",\"resultDesc\":\"成功\"}";
        return JSONUtil.parseObj(str).getJSONObject("data");
    }

    private static void orderCreate(JSONObject doctorInfo, JSONObject scheduleInfo,
                                    JSONObject payInfo, JSONObject costInfo, JSONObject card) {
        JSONConfig config = JSONConfig.create().setIgnoreNullValue(false);
        config.setOrder(true);
        JSONObject body = new JSONObject(config);

        body.putOpt("applyId", "");
        body.putOpt("affiliatedHospital", "");
        body.putOpt("bindingId", card.getStr("bindingId"));
        body.putOpt("branchCode", branchCode);
        body.putOpt("branchName", branchName);
        body.putOpt("clinicUnitId", "");
        body.putOpt("deptId", deptId);
        body.putOpt("deptName", URLUtil.encode(deptName));
        body.putOpt("diseaseId", null);
        body.putOpt("diseaseName", null);

        //=======================doctorInfo=======================
        body.putOpt("doctorId", doctorInfo.getStr("doctorId"));
        body.putOpt("doctorName", doctorInfo.getStr("doctorName"));
        body.putOpt("doctorTitle", doctorInfo.getStr("doctorTitle"));
        body.putOpt("doctorLevelCode", doctorInfo.getStr("doctorLevelCode"));

        //=======================scheduleInfo=======================
        body.putOpt("scheduleId", scheduleInfo.getStr("scheduleId"));
        // 这两个值实际请求下单时发现一样的
        body.putOpt("queueSn", scheduleInfo.getStr("periodId"));
        body.putOpt("periodId", scheduleInfo.getStr("periodId"));
        body.putOpt("serviceItemId", "");
        body.putOpt("timeFlag", scheduleInfo.getStr("timeFlag"));

        //=======================支付相关=======================
        body.putOpt("svObjectId", payInfo.getStr("svObjectId"));
        body.putOpt("svObjectName", payInfo.getStr("svObject"));
        body.putOpt("svMode", payInfo.getStr("svMode"));

        //=======================costInfo=======================
        body.putOpt("cashFee", costInfo.getStr("cashFee"));
        // cost接口返回空,但是实际需要值,从scheduleNew接口获取
        body.putOpt("treatFee", scheduleInfo.getStr("treatFee")); // 挂号费
        body.putOpt("regFee", scheduleInfo.getStr("regFee")); // 0
        body.putOpt("yhFee", costInfo.getStr("yhFee")); // "0.0"
        body.putOpt("insuranFee", costInfo.getStr("insuranFee")); //""
        body.putOpt("medicareSettleLogId", costInfo.getStr("medicareSettleLogId")); // ""

        //=======================就诊人信息=======================
        body.putOpt("patientId", card.getStr("patId"));
        body.putOpt("idCardNo", card.getStr("patIdno").split(",")[0]);
        body.putOpt("phone", card.getStr("phoneNo").split(",")[0]);
        body.putOpt("orderType", "");
        body.putOpt("registerTypeId", "");

        // scheduleInfo里的信息
        body.putOpt("startTime", scheduleInfo.getStr("startTime"));
        body.putOpt("endTime", scheduleInfo.getStr("endTime"));
        body.putOpt("regDate", scheduleInfo.getStr("regDate"));
        body.putOpt("shiftName", scheduleInfo.getStr("shiftName"));
        body.putOpt("remark", "");
        body.putOpt("roomAddress", "");
        body.putOpt("connect_redirect", 1);
        body.putOpt("guidanceCallback", "");
        body.putOpt("ajaxConfig", true);
        body.putOpt("isTencentHealth", "");
        body.putOpt("tencentHealthId", "");
        body.putOpt("alipayHealthId", "");

        String url = domain + "gateway/registration/appointment/order/create";
        String resp = HttpRequest.post(url).cookie(cookie).body(body.toString()).execute().body();
        System.out.println("下单参数:" + body);
        System.out.println("下单结果:" + resp);
    }

    private static void orderCreateAsync(JSONObject doctorInfo, JSONObject scheduleInfo,
                                         JSONObject payInfo, JSONObject costInfo, JSONObject card, int count, int sleepTime) {
        for (int i = 0; i < count; i++) {
            ThreadUtil.sleep(sleepTime, TimeUnit.SECONDS);
            new Thread(() -> {
                orderCreate(doctorInfo, scheduleInfo, payInfo, costInfo, card);
            }).start();
        }
    }

    public static void main(String[] args) throws InterruptedException {
        // 患者信息,这里直接打印json
        JSONObject card = findCard("崔晨光");
        System.out.println("就诊卡信息:" + card);

        // 支付方式信息
        JSONObject payInfo = svObjectFind(card.getStr("bindingId"), branchCode, deptId, "05");
        System.out.println("支付方式:" + payInfo);

        // cost信息
        JSONObject costInfo = costFind();
        System.out.println("cost:" + costInfo);

        // 获取医生排班信息
        getDoctorSchedule(payInfo, costInfo, card, branchCode, deptId, deptName, doctorId, startDate, endDate);
    }
}
