from PIL import Image
import os
from math import ceil, sqrt
import gc
from io import BytesIO

def get_image_files(directory):
    """获取指定目录下的所有JPG图片"""
    image_files = []
    for file in os.listdir(directory):
        if file.lower().endswith('.jpg'):
            image_files.append(os.path.join(directory, file))
    return image_files

def calculate_grid_size(n):
    """计算最佳网格大小"""
    grid_width = ceil(sqrt(n))
    grid_height = ceil(n / grid_width)
    return grid_width, grid_height

def resize_image(image_path, max_dimension=800, quality=85):
    """按比例缩放图片，保持宽高比，并压缩质量"""
    with Image.open(image_path) as img:
        # 转换为RGB模式
        if img.mode != 'RGB':
            img = img.convert('RGB')
            
        # 计算缩放比例
        width, height = img.size
        scale = min(max_dimension / width, max_dimension / height)
        
        if scale < 1:  # 只有当图片大于目标尺寸时才缩小
            new_width = int(width * scale)
            new_height = int(height * scale)
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 创建一个临时的BytesIO对象来压缩图片
        temp_buffer = BytesIO()
        img.save(temp_buffer, format='JPEG', quality=quality, optimize=True)
        temp_buffer.seek(0)
        compressed_img = Image.open(temp_buffer)
        return compressed_img.copy()

def create_collage(directory, output_path, max_dimension=800):
    """创建图片拼贴"""
    # 获取所有图片文件
    image_files = get_image_files(directory)
    n_images = len(image_files)
    print(f"找到 {n_images} 张图片")
    
    # 计算网格大小
    grid_width, grid_height = calculate_grid_size(n_images)
    print(f"网格大小: {grid_width} x {grid_height}")
    
    # 第一次遍历：计算每个缩放后图片的尺寸
    max_col_widths = [0] * grid_width
    max_row_heights = [0] * grid_height
    
    for idx, image_path in enumerate(image_files):
        row = idx // grid_width
        col = idx % grid_width
        
        img = resize_image(image_path, max_dimension)
        width, height = img.size
        
        max_col_widths[col] = max(max_col_widths[col], width)
        max_row_heights[row] = max(max_row_heights[row], height)
        
        del img
        gc.collect()  # 释放内存
    
    # 计算最终拼贴图的尺寸
    total_width = sum(max_col_widths)
    total_height = sum(max_row_heights)
    
    # 创建空白画布
    collage = Image.new('RGB', (total_width, total_height), (255, 255, 255))
    
    # 第二次遍历：粘贴图片
    current_y = 0
    for row in range(grid_height):
        current_x = 0
        for col in range(grid_width):
            idx = row * grid_width + col
            if idx < n_images:
                img = resize_image(image_files[idx], max_dimension)
                
                # 计算居中位置
                x_center = current_x + (max_col_widths[col] - img.size[0]) // 2
                y_center = current_y + (max_row_heights[row] - img.size[1]) // 2
                
                # 粘贴图片
                collage.paste(img, (x_center, y_center))
                
                del img
                gc.collect()  # 释放内存
            
            current_x += max_col_widths[col]
        current_y += max_row_heights[row]
    
    # 保存结果并检查文件大小
    quality = 85
    max_size = 20 * 1024 * 1024  # 20MB in bytes
    
    while True:
        collage.save(output_path, 'JPEG', quality=quality, optimize=True)
        file_size = os.path.getsize(output_path)
        
        if file_size <= max_size or quality <= 5:
            break
            
        # 如果文件太大，降低质量并重试
        quality = max(quality - 5, 5)
        print(f"文件大小: {file_size/1024/1024:.2f}MB, 降低质量至: {quality}")
    
    print(f"拼贴图已保存至: {output_path}")
    print(f"最终文件大小: {os.path.getsize(output_path)/1024/1024:.2f}MB")

if __name__ == "__main__":
    # 设置输入输出路径
    input_directory = r"D:\小宝照片\全部"
    output_path = r"D:\小宝照片\collage_result.jpg"
    
    # 创建拼贴图
    create_collage(input_directory, output_path)